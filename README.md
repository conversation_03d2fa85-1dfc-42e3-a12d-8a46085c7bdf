# Arc Common Logger SDK

一个基于spdlog的C++日志封装SDK，支持文件轮转和异步日志记录。

## 特性

- **文件轮转**: 自动轮转日志文件，支持配置文件大小和保留文件数量
- **异步日志**: 可选的异步日志模式，提高性能
- **多线程安全**: 支持多线程环境下的并发日志记录
- **多种日志级别**: 支持 TRACE, DEBUG, INFO, WARN, ERROR, CRITICAL
- **简单易用**: 单例模式，简化使用
- **灵活配置**: 支持自定义日志格式、文件路径等

## 目录结构

```
spdlog_utils/
├── Related_Project/spdlog/    # 编译好的spdlog库
├── sdk/                       # SDK源码
│   ├── inc/
│   │   └── arc_common_logger.h
│   ├── src/
│   │   └── arc_common_logger.cpp
│   └── Makefile
├── examples/                  # 使用示例
│   └── simple_example.cpp
└── README.md
```

## 编译

### 前提条件

- C++17 或更高版本
- 已编译的spdlog库（位于 Related_Project/spdlog/）
- 支持 std::filesystem 的编译器

### 编译SDK

```bash
cd sdk
make all          # 编译静态库和动态库
make static       # 仅编译静态库
make shared       # 仅编译动态库
make examples     # 编译示例程序
```

### 编译选项

```bash
make debug        # 调试版本
make release      # 发布版本
make install      # 安装到系统
```

## 使用方法

### 基本使用

```cpp
#include "arc_common_logger.h"

int main() {
    // 配置日志
    arc::LoggerConfig config;
    config.log_file_path = "logs/app.log";
    config.max_file_size = 10 * 1024 * 1024;  // 10MB
    config.max_files = 5;
    config.level = arc::LogLevel::INFO;
    config.async_mode = false;  // 同步模式
    
    // 初始化日志器
    auto& logger = arc::ArcCommonLogger::getInstance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize logger!" << std::endl;
        return 1;
    }
    
    // 记录日志
    logger.info("Application started");
    logger.warn("This is a warning");
    logger.error("This is an error");
    
    // 或使用宏
    ARC_LOG_INFO("Using macro for logging");
    ARC_LOG_ERROR("Error occurred");
    
    // 关闭日志器
    logger.shutdown();
    return 0;
}
```

### 异步日志配置

```cpp
arc::LoggerConfig config;
config.log_file_path = "logs/async_app.log";
config.async_mode = true;           // 启用异步模式
config.async_queue_size = 8192;     // 异步队列大小
config.max_file_size = 50 * 1024 * 1024;  // 50MB
config.max_files = 10;
```

### 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `log_file_path` | string | "logs/app.log" | 日志文件路径 |
| `max_file_size` | size_t | 10MB | 单个日志文件最大大小 |
| `max_files` | size_t | 5 | 保留的日志文件数量 |
| `level` | LogLevel | INFO | 日志级别 |
| `async_mode` | bool | false | 是否启用异步模式 |
| `async_queue_size` | size_t | 8192 | 异步队列大小 |
| `pattern` | string | "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v" | 日志格式 |

### 日志级别

- `TRACE`: 最详细的日志信息
- `DEBUG`: 调试信息
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误
- `OFF`: 关闭日志

## 示例程序

运行示例程序：

```bash
cd sdk
make examples
../build/bin/simple_example
```

示例程序演示了：
- 同步日志记录
- 异步日志记录
- 多线程日志记录
- 日志文件轮转

## API 参考

### 主要方法

- `getInstance()`: 获取单例实例
- `initialize(config)`: 初始化日志器
- `isInitialized()`: 检查是否已初始化
- `setLevel(level)`: 设置日志级别
- `trace/debug/info/warn/error/critical(message)`: 记录不同级别的日志
- `flush()`: 刷新日志缓冲区
- `shutdown()`: 关闭日志器

### 便利宏

- `ARC_LOG_TRACE(msg)`
- `ARC_LOG_DEBUG(msg)`
- `ARC_LOG_INFO(msg)`
- `ARC_LOG_WARN(msg)`
- `ARC_LOG_ERROR(msg)`
- `ARC_LOG_CRITICAL(msg)`

## 性能特性

- **异步模式**: 在高频日志场景下显著提升性能
- **文件轮转**: 自动管理日志文件大小，避免单个文件过大
- **线程安全**: 内置互斥锁保护，支持多线程并发
- **内存效率**: 使用PIMPL模式减少头文件依赖

## 注意事项

1. 使用异步模式时，程序退出前务必调用 `shutdown()` 确保所有日志写入
2. 日志文件路径的目录会自动创建
3. 文件轮转按文件大小触发，旧文件会添加数字后缀
4. 单例模式确保全局只有一个日志器实例

## 许可证

本项目基于spdlog库开发，遵循相应的开源许可证。
