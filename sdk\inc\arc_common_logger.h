#ifndef ARC_COMMON_LOGGER_H
#define ARC_COMMON_LOGGER_H

#include <string>
#include <memory>

namespace arc {

/**
 * @brief Log levels supported by the logger
 */
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    CRITICAL = 5,
    OFF = 6
};

/**
 * @brief Configuration for the rotating file logger
 */
struct LoggerConfig {
    std::string log_file_path = "logs/app.log";     // Log file path
    size_t max_file_size = 10 * 1024 * 1024;       // 10MB default
    size_t max_files = 5;                           // Keep 5 files by default
    LogLevel level = LogLevel::INFO;                // Default log level
    bool async_mode = false;                        // Synchronous by default
    size_t async_queue_size = 8192;                 // Async queue size (only used if async_mode = true)
    std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v"; // Log pattern

    LoggerConfig() = default;
};

/**
 * @brief Arc Common Logger - A wrapper around spdlog with rotating file sink
 *
 * This class provides a simple interface for logging with automatic file rotation.
 * It supports both synchronous and asynchronous logging modes.
 */
class ArcCommonLogger {
public:
    /**
     * @brief Constructor
     */
    ArcCommonLogger();

    /**
     * @brief Destructor
     */
    ~ArcCommonLogger();

    /**
     * @brief Initialize the logger with configuration
     * @param config Logger configuration
     * @return true if initialization successful, false otherwise
     */
    bool initialize(const LoggerConfig& config);

    /**
     * @brief Check if logger is initialized
     * @return true if initialized, false otherwise
     */
    bool isInitialized() const;

    /**
     * @brief Set the log level
     * @param level New log level
     */
    void setLevel(LogLevel level);

    /**
     * @brief Get current log level
     * @return Current log level
     */
    LogLevel getLevel() const;

    /**
     * @brief Log a trace message
     * @param message Message to log
     */
    void trace(const std::string& message);

    /**
     * @brief Log a debug message
     * @param message Message to log
     */
    void debug(const std::string& message);

    /**
     * @brief Log an info message
     * @param message Message to log
     */
    void info(const std::string& message);

    /**
     * @brief Log a warning message
     * @param message Message to log
     */
    void warn(const std::string& message);

    /**
     * @brief Log an error message
     * @param message Message to log
     */
    void error(const std::string& message);

    /**
     * @brief Log a critical message
     * @param message Message to log
     */
    void critical(const std::string& message);

    /**
     * @brief Template function for formatted logging
     * @tparam Args Argument types
     * @param level Log level
     * @param format Format string
     * @param args Arguments for formatting
     */
    template<typename... Args>
    void log(LogLevel level, const std::string& format, Args&&... args);

    /**
     * @brief Flush all pending log messages
     */
    void flush();

    /**
     * @brief Shutdown the logger and cleanup resources
     */
    void shutdown();

private:
    /**
     * @brief Create directory if it doesn't exist
     * @param path Directory path
     * @return true if directory exists or was created successfully
     */
    bool createDirectoryIfNotExists(const std::string& path);

    /**
     * @brief Convert LogLevel to spdlog level
     * @param level Our log level
     * @return spdlog log level
     */
    int toSpdlogLevel(LogLevel level) const;

private:
    class Impl;                          // Forward declaration for PIMPL
    std::unique_ptr<Impl> pImpl;         // PIMPL pointer
    bool m_initialized;                  // Initialization flag
    LoggerConfig m_config;               // Current configuration
};

// Template implementation
template<typename... Args>
void ArcCommonLogger::log(LogLevel level, const std::string& format, Args&&... args) {
    if (!m_initialized) {
        return;
    }

    // Implementation will be in the cpp file
    // This is just the declaration
}

} // namespace arc

// Convenience macros for easier usage
#define ARC_LOG_TRACE(logger, msg)    (logger).trace(msg)
#define ARC_LOG_DEBUG(logger, msg)    (logger).debug(msg)
#define ARC_LOG_INFO(logger, msg)     (logger).info(msg)
#define ARC_LOG_WARN(logger, msg)     (logger).warn(msg)
#define ARC_LOG_ERROR(logger, msg)    (logger).error(msg)
#define ARC_LOG_CRITICAL(logger, msg) (logger).critical(msg)

#endif // ARC_COMMON_LOGGER_H
