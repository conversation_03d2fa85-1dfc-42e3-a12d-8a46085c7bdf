#include "arc_common_logger.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>

void demonstrateSyncLogging() {
    std::cout << "\n=== Demonstrating Synchronous Logging ===" << std::endl;

    // 配置同步日志
    arc::LoggerConfig config;
    config.log_file_path = "logs/sync_example.log";
    config.max_file_size = 1024 * 1024;  // 1MB
    config.max_files = 3;
    config.level = arc::LogLevel::DEBUG;
    config.async_mode = false;  // 同步模式

    // 创建并初始化logger实例
    arc::ArcCommonLogger logger;
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize logger!" << std::endl;
        return;
    }

    std::cout << "Logger initialized successfully (sync mode)" << std::endl;

    // 使用实例记录日志
    logger.trace("This is a trace message");
    logger.debug("This is a debug message");
    logger.info("This is an info message");
    
    // 使用修改后的宏
    ARC_LOG_INFO(logger, "Using macro for info logging");
    ARC_LOG_WARN(logger, "Using macro for warning logging");
    
    // 刷新并关闭
    logger.flush();
}

void demonstrateAsyncLogging() {
    std::cout << "\n=== Demonstrating Asynchronous Logging ===" << std::endl;

    // Configure asynchronous logging
    arc::LoggerConfig config;
    config.log_file_path = "logs/async_example.log";
    config.max_file_size = 1024 * 1024;  // 1MB
    config.max_files = 3;
    config.level = arc::LogLevel::DEBUG;
    config.async_mode = true;   // Asynchronous mode
    config.async_queue_size = 8192;

    // Initialize logger
    auto& logger = arc::ArcCommonLogger::getInstance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize async logger!" << std::endl;
        return;
    }

    std::cout << "Logger initialized successfully (async mode)" << std::endl;

    // Test rapid logging to demonstrate async performance
    auto start_time = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < 1000; ++i) {
        logger.info("Async log message number: " + std::to_string(i));
        if (i % 100 == 0) {
            logger.warn("Checkpoint: " + std::to_string(i) + " messages logged");
        }
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    logger.info("Logged 1000 messages in " + std::to_string(duration.count()) + " milliseconds");

    // Flush to ensure all messages are written
    logger.flush();

    std::cout << "Asynchronous logging completed in " << duration.count() << "ms" << std::endl;
    std::cout << "Check logs/async_example.log" << std::endl;
}

void demonstrateMultiThreadLogging() {
    std::cout << "\n=== Demonstrating Multi-threaded Logging ===" << std::endl;

    // Configure for multi-threaded logging
    arc::LoggerConfig config;
    config.log_file_path = "logs/multithread_example.log";
    config.max_file_size = 2 * 1024 * 1024;  // 2MB
    config.max_files = 5;
    config.level = arc::LogLevel::INFO;
    config.async_mode = true;  // Async is better for multi-threading
    config.async_queue_size = 16384;

    // Initialize logger
    auto& logger = arc::ArcCommonLogger::getInstance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize multi-thread logger!" << std::endl;
        return;
    }

    std::cout << "Logger initialized for multi-threading test" << std::endl;

    // Create multiple threads that log simultaneously
    const int num_threads = 4;
    const int messages_per_thread = 250;
    std::vector<std::thread> threads;

    auto start_time = std::chrono::high_resolution_clock::now();

    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&logger, t, messages_per_thread]() {
            for (int i = 0; i < messages_per_thread; ++i) {
                std::string message = "Thread " + std::to_string(t) + " - Message " + std::to_string(i);
                logger.info(message);

                if (i % 50 == 0) {
                    logger.debug("Thread " + std::to_string(t) + " checkpoint: " + std::to_string(i));
                }

                // Small delay to simulate real work
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
            logger.warn("Thread " + std::to_string(t) + " completed");
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    logger.critical("Multi-threaded test completed in " + std::to_string(duration.count()) + " milliseconds");
    logger.flush();

    std::cout << "Multi-threaded logging completed in " << duration.count() << "ms" << std::endl;
    std::cout << "Check logs/multithread_example.log" << std::endl;
}

void demonstrateLogRotation() {
    std::cout << "\n=== Demonstrating Log Rotation ===" << std::endl;

    // Configure with small file size to trigger rotation
    arc::LoggerConfig config;
    config.log_file_path = "logs/rotation_example.log";
    config.max_file_size = 1024;  // Very small - 1KB to trigger rotation quickly
    config.max_files = 3;
    config.level = arc::LogLevel::INFO;
    config.async_mode = false;  // Sync to see rotation immediately

    // Initialize logger
    auto& logger = arc::ArcCommonLogger::getInstance();
    if (!logger.initialize(config)) {
        std::cerr << "Failed to initialize rotation logger!" << std::endl;
        return;
    }

    std::cout << "Logger initialized for rotation test (1KB max file size)" << std::endl;

    // Generate enough logs to trigger rotation
    for (int i = 0; i < 100; ++i) {
        std::string long_message = "This is a longer log message number " + std::to_string(i) +
                                  " designed to fill up the log file quickly and trigger rotation. " +
                                  "The message contains some additional text to make it longer.";
        logger.info(long_message);

        if (i % 10 == 0) {
            logger.warn("Rotation checkpoint: " + std::to_string(i) + " messages written");
            logger.flush();  // Force write to see rotation
        }
    }

    logger.critical("Log rotation test completed");
    logger.flush();

    std::cout << "Log rotation test completed." << std::endl;
    std::cout << "Check logs/ directory for rotation_example.log, rotation_example.1.log, etc." << std::endl;
}

int main() {
    std::cout << "Arc Common Logger SDK Example" << std::endl;
    std::cout << "=============================" << std::endl;

    try {
        // Demonstrate different logging modes
        demonstrateSyncLogging();

        // Small delay between tests
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        demonstrateAsyncLogging();

        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        demonstrateMultiThreadLogging();

        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        demonstrateLogRotation();

        std::cout << "\n=== All tests completed successfully! ===" << std::endl;
        std::cout << "Check the logs/ directory for generated log files." << std::endl;

        // Shutdown logger
        arc::ArcCommonLogger::getInstance().shutdown();

    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
