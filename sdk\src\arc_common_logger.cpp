#include "arc_common_logger.h"

#include <spdlog/spdlog.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/async.h>
#include <spdlog/async_logger.h>
#include <filesystem>
#include <iostream>
#include <mutex>

namespace arc {

/**
 * @brief PIMPL implementation class
 */
class ArcCommonLogger::Impl {
public:
    std::shared_ptr<spdlog::logger> logger;
    std::mutex logger_mutex;

    Impl() : logger(nullptr) {}

    ~Impl() {
        if (logger) {
            logger->flush();
        }
    }
};

// Static instance for singleton
// static std::once_flag s_instance_flag;
// static std::unique_ptr<ArcCommonLogger> s_instance;

ArcCommonLogger::ArcCommonLogger()
    : pImpl(std::make_unique<Impl>())
    , m_initialized(false) {
}

ArcCommonLogger::~ArcCommonLogger() {
    shutdown();
}

// ArcCommonLogger& ArcCommonLogger::getInstance() { ... }

bool ArcCommonLogger::createDirectoryIfNotExists(const std::string& path) {
    try {
        std::filesystem::path file_path(path);
        std::filesystem::path dir_path = file_path.parent_path();

        if (!dir_path.empty() && !std::filesystem::exists(dir_path)) {
            return std::filesystem::create_directories(dir_path);
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create directory: " << e.what() << std::endl;
        return false;
    }
}

int ArcCommonLogger::toSpdlogLevel(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE:    return SPDLOG_LEVEL_TRACE;
        case LogLevel::DEBUG:    return SPDLOG_LEVEL_DEBUG;
        case LogLevel::INFO:     return SPDLOG_LEVEL_INFO;
        case LogLevel::WARN:     return SPDLOG_LEVEL_WARN;
        case LogLevel::ERROR:    return SPDLOG_LEVEL_ERROR;
        case LogLevel::CRITICAL: return SPDLOG_LEVEL_CRITICAL;
        case LogLevel::OFF:      return SPDLOG_LEVEL_OFF;
        default:                 return SPDLOG_LEVEL_INFO;
    }
}

bool ArcCommonLogger::initialize(const LoggerConfig& config) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);

    try {
        // Store configuration
        m_config = config;

        // Create directory if needed
        if (!createDirectoryIfNotExists(config.log_file_path)) {
            std::cerr << "Failed to create log directory for: " << config.log_file_path << std::endl;
            return false;
        }

        // Shutdown existing logger if any
        if (pImpl->logger) {
            pImpl->logger->flush();
            spdlog::drop(pImpl->logger->name());
            pImpl->logger.reset();
        }

        // 为每个实例创建唯一的logger名称
        static std::atomic<int> logger_counter{0};
        std::string logger_name = "arc_logger_" + std::to_string(logger_counter++);

        // Create rotating file sink
        auto rotating_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            config.log_file_path,
            config.max_file_size,
            config.max_files
        );

        // Set pattern
        rotating_sink->set_pattern(config.pattern);

        // Create logger based on async mode
        if (config.async_mode) {
            // Initialize async logging
            spdlog::init_thread_pool(config.async_queue_size, 1);

            // Create async logger
            pImpl->logger = std::make_shared<spdlog::async_logger>(
                logger_name,
                rotating_sink,
                spdlog::thread_pool(),
                spdlog::async_overflow_policy::block
            );
        } else {
            // Create synchronous logger
            pImpl->logger = std::make_shared<spdlog::logger>(logger_name, rotating_sink);
        }

        // Set log level
        pImpl->logger->set_level(static_cast<spdlog::level::level_enum>(toSpdlogLevel(config.level)));

        // Register logger with spdlog
        spdlog::register_logger(pImpl->logger);
        
        // 不再设置为默认logger
        // spdlog::set_default_logger(pImpl->logger);

        m_initialized = true;

        // 记录初始化信息
        pImpl->logger->info("Arc Common Logger initialized successfully");
        pImpl->logger->info("Logger name: {}", logger_name);
        pImpl->logger->info("Log file: {}", config.log_file_path);
        pImpl->logger->info("Max file size: {} bytes", config.max_file_size);
        pImpl->logger->info("Max files: {}", config.max_files);
        pImpl->logger->info("Async mode: {}", config.async_mode ? "enabled" : "disabled");

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize logger: " << e.what() << std::endl;
        m_initialized = false;
        return false;
    }
}

bool ArcCommonLogger::isInitialized() const {
    return m_initialized;
}

void ArcCommonLogger::setLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->set_level(static_cast<spdlog::level::level_enum>(toSpdlogLevel(level)));
        m_config.level = level;
    }
}

LogLevel ArcCommonLogger::getLevel() const {
    return m_config.level;
}

void ArcCommonLogger::trace(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->trace(message);
    }
}

void ArcCommonLogger::debug(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->debug(message);
    }
}

void ArcCommonLogger::info(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->info(message);
    }
}

void ArcCommonLogger::warn(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->warn(message);
    }
}

void ArcCommonLogger::error(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->error(message);
    }
}

void ArcCommonLogger::critical(const std::string& message) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->critical(message);
    }
}

void ArcCommonLogger::flush() {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->flush();
    }
}

void ArcCommonLogger::shutdown() {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (pImpl->logger) {
        pImpl->logger->info("Arc Common Logger shutting down");
        pImpl->logger->flush();
        spdlog::drop(pImpl->logger->name());
        pImpl->logger.reset();
    }

    if (m_config.async_mode) {
        spdlog::shutdown();
    }

    m_initialized = false;
}

// Template specializations for formatted logging
template<>
void ArcCommonLogger::log<>(LogLevel level, const std::string& format) {
    std::lock_guard<std::mutex> lock(pImpl->logger_mutex);
    if (!pImpl->logger) return;

    auto spdlog_level = static_cast<spdlog::level::level_enum>(toSpdlogLevel(level));
    pImpl->logger->log(spdlog_level, format);
}

} // namespace arc
